import logger from '@/lib/utils/logger';

import { NextRequest, NextResponse } from "next/server"
import { TempAuthFix } from "@/temp_auth_fix"
import { PermissionInitService } from "@/lib/services/permission-init-service"

/**
 * 临时权限刷新API
 * 使用增强的认证检查来修复权限刷新问题
 */
export async function POST(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 10)
  logger.debug(`[临时权限刷新API:${requestId}] 开始刷新权限系统`)

  try {
    // 使用临时认证修复获取用户
    const user = await TempAuthFix.getCurrentUserForPermissionAPI(request);
    
    if (!user) {
      logger.error(`[临时权限刷新API:${requestId}] 未找到有效的认证信息`)
      return NextResponse.json({
        success: false,
        message: "未授权访问，请确保您已登录并具有管理员权限",
        requestId
      }, { status: 401 })
    }

    // 检查用户是否为管理员
    if (!TempAuthFix.isAdmin(user)) {
      logger.error(`[临时权限刷新API:${requestId}] 用户没有管理员权限:`, user.username, user.roleCode)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以刷新权限系统",
        requestId
      }, { status: 403 })
    }

    logger.log(`[临时权限刷新API:${requestId}] 管理员 ${user.username} 开始刷新权限系统`)

    // 初始化所有权限
    const success = await PermissionInitService.initAllPermissions()

    if (success) {
      logger.log(`[临时权限刷新API:${requestId}] 权限系统刷新成功`)
      return NextResponse.json({
        success: true,
        message: "权限系统刷新成功",
        requestId
      })
    } else {
      logger.error(`[临时权限刷新API:${requestId}] 权限系统刷新失败`)
      return NextResponse.json({
        success: false,
        message: "权限系统刷新失败",
        requestId
      }, { status: 500 })
    }
  } catch (error) {
    logger.error(`[临时权限刷新API:${requestId}] 刷新权限系统失败:`, error)
    return NextResponse.json({
      success: false,
      message: "刷新权限系统失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}
