declare module "dgram" {
    import { AddressInfo } from "net";
    import * as dns from "dns";
    import EventEmitter = require("events");

    interface RemoteInfo {
        address: string;
        family: "IPv4" | "IPv6";
        port: number;
        size: number;
    }

    interface BindOptions {
        port?: number | undefined;
        address?: string | undefined;
        exclusive?: boolean | undefined;
        fd?: number | undefined;
    }

    type SocketType = "udp4" | "udp6";

    interface SocketOptions {
        type: SocketType;
        reuseAddr?: boolean | undefined;
        /**
         * @default false
         */
        ipv6Only?: boolean | undefined;
        recvBufferSize?: number | undefined;
        sendBufferSize?: number | undefined;
        lookup?:
            | ((
                hostname: string,
                options: dns.LookupOneOptions,
                callback: (err: NodeJS.ErrnoException | null, address: string, family: number) => void,
            ) => void)
            | undefined;
    }

    function createSocket(type: SocketType, callback?: (msg: Buffer, rinfo: RemoteInfo) => void): Socket;
    function createSocket(options: SocketOptions, callback?: (msg: <PERSON>uff<PERSON>, rinfo: RemoteInfo) => void): Socket;

    class Socket extends EventEmitter {
        addMembership(multicastAddress: string, multicastInterface?: string): void;
        address(): AddressInfo;
        bind(port?: number, address?: string, callback?: () => void): this;
        bind(port?: number, callback?: () => void): this;
        bind(callback?: () => void): this;
        bind(options: BindOptions, callback?: () => void): this;
        close(callback?: () => void): this;
        connect(port: number, address?: string, callback?: () => void): void;
        connect(port: number, callback: () => void): void;
        disconnect(): void;
        dropMembership(multicastAddress: string, multicastInterface?: string): void;
        getRecvBufferSize(): number;
        getSendBufferSize(): number;
        ref(): this;
        remoteAddress(): AddressInfo;
        send(
            msg: string | Uint8Array | ReadonlyArray<any>,
            port?: number,
            address?: string,
            callback?: (error: Error | null, bytes: number) => void,
        ): void;
        send(
            msg: string | Uint8Array | ReadonlyArray<any>,
            port?: number,
            callback?: (error: Error | null, bytes: number) => void,
        ): void;
        send(
            msg: string | Uint8Array | ReadonlyArray<any>,
            callback?: (error: Error | null, bytes: number) => void,
        ): void;
        send(
            msg: string | Uint8Array,
            offset: number,
            length: number,
            port?: number,
            address?: string,
            callback?: (error: Error | null, bytes: number) => void,
        ): void;
        send(
            msg: string | Uint8Array,
            offset: number,
            length: number,
            port?: number,
            callback?: (error: Error | null, bytes: number) => void,
        ): void;
        send(
            msg: string | Uint8Array,
            offset: number,
            length: number,
            callback?: (error: Error | null, bytes: number) => void,
        ): void;
        setBroadcast(flag: boolean): void;
        setMulticastInterface(multicastInterface: string): void;
        setMulticastLoopback(flag: boolean): boolean;
        setMulticastTTL(ttl: number): number;
        setRecvBufferSize(size: number): void;
        setSendBufferSize(size: number): void;
        setTTL(ttl: number): number;
        unref(): this;
        /**
         * Tells the kernel to join a source-specific multicast channel at the given
         * `sourceAddress` and `groupAddress`, using the `multicastInterface` with the
         * `IP_ADD_SOURCE_MEMBERSHIP` socket option.
         * If the `multicastInterface` argument
         * is not specified, the operating system will choose one interface and will add
         * membership to it.
         * To add membership to every available interface, call
         * `socket.addSourceSpecificMembership()` multiple times, once per interface.
         */
        addSourceSpecificMembership(sourceAddress: string, groupAddress: string, multicastInterface?: string): void;

        /**
         * Instructs the kernel to leave a source-specific multicast channel at the given
         * `sourceAddress` and `groupAddress` using the `IP_DROP_SOURCE_MEMBERSHIP`
         * socket option. This method is automatically called by the kernel when the
         * socket is closed or the process terminates, so most apps will never have
         * reason to call this.
         *
         * If `multicastInterface` is not specified, the operating system will attempt to
         * drop membership on all valid interfaces.
         */
        dropSourceSpecificMembership(sourceAddress: string, groupAddress: string, multicastInterface?: string): void;

        /**
         * events.EventEmitter
         * 1. close
         * 2. connect
         * 3. error
         * 4. listening
         * 5. message
         */
        addListener(event: string, listener: (...args: any[]) => void): this;
        addListener(event: "close", listener: () => void): this;
        addListener(event: "connect", listener: () => void): this;
        addListener(event: "error", listener: (err: Error) => void): this;
        addListener(event: "listening", listener: () => void): this;
        addListener(event: "message", listener: (msg: Buffer, rinfo: RemoteInfo) => void): this;

        emit(event: string | symbol, ...args: any[]): boolean;
        emit(event: "close"): boolean;
        emit(event: "connect"): boolean;
        emit(event: "error", err: Error): boolean;
        emit(event: "listening"): boolean;
        emit(event: "message", msg: Buffer, rinfo: RemoteInfo): boolean;

        on(event: string, listener: (...args: any[]) => void): this;
        on(event: "close", listener: () => void): this;
        on(event: "connect", listener: () => void): this;
        on(event: "error", listener: (err: Error) => void): this;
        on(event: "listening", listener: () => void): this;
        on(event: "message", listener: (msg: Buffer, rinfo: RemoteInfo) => void): this;

        once(event: string, listener: (...args: any[]) => void): this;
        once(event: "close", listener: () => void): this;
        once(event: "connect", listener: () => void): this;
        once(event: "error", listener: (err: Error) => void): this;
        once(event: "listening", listener: () => void): this;
        once(event: "message", listener: (msg: Buffer, rinfo: RemoteInfo) => void): this;

        prependListener(event: string, listener: (...args: any[]) => void): this;
        prependListener(event: "close", listener: () => void): this;
        prependListener(event: "connect", listener: () => void): this;
        prependListener(event: "error", listener: (err: Error) => void): this;
        prependListener(event: "listening", listener: () => void): this;
        prependListener(event: "message", listener: (msg: Buffer, rinfo: RemoteInfo) => void): this;

        prependOnceListener(event: string, listener: (...args: any[]) => void): this;
        prependOnceListener(event: "close", listener: () => void): this;
        prependOnceListener(event: "connect", listener: () => void): this;
        prependOnceListener(event: "error", listener: (err: Error) => void): this;
        prependOnceListener(event: "listening", listener: () => void): this;
        prependOnceListener(event: "message", listener: (msg: Buffer, rinfo: RemoteInfo) => void): this;
    }
}
declare module "node:dgram" {
    export * from "dgram";
}
