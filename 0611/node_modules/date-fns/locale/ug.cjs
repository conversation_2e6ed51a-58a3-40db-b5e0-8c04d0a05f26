"use strict";
exports.ug = void 0;
var _index = require("./ug/_lib/formatDistance.cjs");
var _index2 = require("./ug/_lib/formatLong.cjs");
var _index3 = require("./ug/_lib/formatRelative.cjs");
var _index4 = require("./ug/_lib/localize.cjs");
var _index5 = require("./ug/_lib/match.cjs");

/**
 * @category Locales
 * @summary Uighur locale
 * @language Uighur
 * @iso-639-2 uig
 * <AUTHOR> M. [@abduwaly](https://github.com/abduwaly)
 */
const ug = (exports.ug = {
  code: "ug",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
});
