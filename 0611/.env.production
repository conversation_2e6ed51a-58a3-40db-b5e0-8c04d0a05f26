# DevBox生产环境配置
NODE_ENV=production

# 数据库配置
DATABASE_URL="postgresql://postgres:<EMAIL>:5432/5gweb"
POSTGRES_USER=postgres
POSTGRES_PASSWORD=lpr8h66s
POSTGRES_HOST=web5g-postgresql.ns-rzavvov7.svc
POSTGRES_PORT=5432
POSTGRES_DB=5gweb

# Redis配置
REDIS_URL="redis://default:<EMAIL>:6379"
REDIS_HOST=web5g2025-redis.ns-rzavvov7.svc
REDIS_PORT=6379
REDIS_PASSWORD=54m6q4wb
REDIS_USERNAME=default

# NextAuth配置
NEXTAUTH_URL=https://ooaclkofmixc.sealoshzh.site
NEXTAUTH_SECRET=your-nextauth-secret-key-here-change-in-production

# JWT配置
JWT_SECRET=your-jwt-secret-key-here-change-in-production

# API配置
NEXT_PUBLIC_API_BASE_URL=https://services.vcrm.vip:8000
API_BASE_URL=https://services.vcrm.vip:8000

# 邮件配置
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
SMTP_FROM=<EMAIL>

# 文件上传配置
UPLOAD_DIR=/tmp/uploads
MAX_FILE_SIZE=10485760

# 安全配置
BCRYPT_ROUNDS=12
SESSION_TIMEOUT=1800

# 日志配置
LOG_LEVEL=info
