import logger from '@/lib/utils/logger';

import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import * as jose from 'jose'
import { getToken } from "next-auth/jwt"
import { v4 as uuidv4 } from 'uuid'

const JWT_SECRET = new TextEncoder().encode(
  process.env.NEXTAUTH_SECRET || process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'
)

// 需要认证的路径
const AUTH_PATHS = [
  "/api/user/profile",
  "/api/user/notifications/settings",
  "/api/user/login-history",
  "/api/customers",
  "/api/notifications/unread-count",
  "/user",
  "/user/profile",
  "/user/settings",
  "/account-profile",  // 个人资料路径
  "/user/history"
  // 移除 "/dashboard" - 让NextAuth在页面级别处理认证
]

// 需要认证的布局路径
const AUTH_LAYOUTS = [
  "/(dashboard)",
  "/user"
]

// 需要权限检查的API路径
const PROTECTED_PATHS = [
  "/api/resources",
  "/api/user/profile"
]

// 白名单路径
const PUBLIC_PATHS = [
  "/login",
  "/register",
  "/forgot-password",
  "/dashboard",  // 暂时添加到白名单，让NextAuth在页面级别处理认证
  "/api/auth",  // 所有NextAuth相关路径
  "/_next",
  "/favicon.ico",
  "/static",
  "/images",
  "/fonts",
  "/api/auth/me",
  "/api/auth/callback",
  "/api/auth/check-permission",
  "/api/auth/csrf",
  "/api/auth/session"
]

// 生成唯一请求ID用于日志跟踪
function generateRequestId() {
  return uuidv4().substring(0, 8);
}

// 获取认证信息
async function getAuthInfo(request: NextRequest, requestId: string) {
  try {
    // 1. 首先尝试获取 NextAuth session
    const sessionToken = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
      cookieName: process.env.NODE_ENV === 'production'
        ? '__Secure-next-auth.session-token'
        : 'next-auth.session-token'
    })

    logger.debug(`[中间件:${requestId}] NextAuth 会话令牌:`, sessionToken)

    if (sessionToken && sessionToken.sub) {
      // 使用sub字段作为用户ID（NextAuth标准）
      return {
        isAuthenticated: true,
        userId: sessionToken.sub as string,
        roleCode: sessionToken.roleCode as string,
        permissions: sessionToken.permissions as string[],
        authType: 'nextauth'
      }
    }

    // 2. 如果没有 NextAuth session，尝试获取和验证 JWT token
    const token = request.cookies.get("token")?.value

    if (!token) {
      logger.debug(`[中间件:${requestId}] 未找到任何认证令牌`)
      return { isAuthenticated: false, authType: 'none' }
    }

    try {
      const { payload } = await jose.jwtVerify(token, JWT_SECRET)
      if (!payload.sub) {
        logger.warn(`[中间件:${requestId}] JWT令牌无效，缺少 sub 字段`)
        return { isAuthenticated: false, authType: 'invalid' }
      }

      logger.debug(`[中间件:${requestId}] JWT令牌有效, 用户ID: ${payload.sub}`)
      return {
        isAuthenticated: true,
        userId: payload.sub as string,
        roleCode: payload.role as string,
        permissions: payload.permissions as string[] || [],
        authType: 'jwt'
      }
    } catch (error) {
      logger.error(`[中间件:${requestId}] JWT Token验证失败:`, error)
      return { isAuthenticated: false, authType: 'expired' }
    }
  } catch (error) {
    logger.error(`[中间件:${requestId}] 认证检查失败:`, error)
    return { isAuthenticated: false, authType: 'error' }
  }
}

// 验证重定向路径安全性
function validateRedirectUrl(url: string): boolean {
  // 如果URL为空或者undefined，视为不安全
  if (!url) return false;

  // 检查是否是相对URL（不包含协议与域名）
  if (url.startsWith('http://') || url.startsWith('https://')) {
    // 如果是绝对URL，验证是否是本站域名
    try {
      const urlObj = new URL(url);
      // 检查域名是否与当前站点相同
      const hostname = process.env.NEXTAUTH_URL
        ? new URL(process.env.NEXTAUTH_URL).hostname
        : 'localhost';

      return urlObj.hostname === hostname;
    } catch (e) {
      return false;
    }
  }

  // 验证相对URL是否从根路径开始且不包含特殊字符
  return url.startsWith('/') &&
    !url.includes('\\') &&
    !url.includes('%') &&
    !(/[<>"']/).test(url);
}

// 检查用户是否有指定权限
async function checkUserPermission(userId: string, resource: string, action: string, requestId: string) {
  try {
    // 这里可以集成jCasbin进行权限验证
    // 为了简单演示，我们先使用基本判断
    logger.debug(`[中间件:${requestId}] 检查权限: 用户=${userId}, 资源=${resource}, 操作=${action}`)

    // 实际生产环境应该使用jCasbin进行检查
    // 例如: const allowed = await enforcer.enforce(userId, resource, action);
    return true; // 临时默认允许，后续需要接入jCasbin
  } catch (error) {
    logger.error(`[中间件:${requestId}] 权限检查失败:`, error);
    return false;
  }
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  const requestId = generateRequestId()
  logger.debug(`[中间件:${requestId}] 处理请求: ${pathname}`)

  // 处理 OPTIONS 请求，添加 CORS 头
  if (request.method === 'OPTIONS') {
    // 获取域名
    const origin = request.headers.get('origin') || "https://ooaclkofmixc.sealoshzh.site"

    return new NextResponse(null, {
      status: 204,
      headers: {
        "Access-Control-Allow-Origin": origin,
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Auth-Timestamp, X-Auth-Signature, X-Auth-App-Id, X-Auth-Login-Name, X-Auth-Org-Code",
        "Access-Control-Allow-Credentials": "true",
        "Access-Control-Max-Age": "86400",
      },
    });
  }

  // 安全验证重定向URL参数
  if (request.nextUrl.searchParams.has('returnUrl')) {
    const returnUrl = request.nextUrl.searchParams.get('returnUrl') as string
    if (!validateRedirectUrl(returnUrl)) {
      logger.warn(`[中间件:${requestId}] 检测到不安全的重定向URL: ${returnUrl}`)

      // 删除不安全的returnUrl参数
      const safeUrl = new URL(request.url)
      safeUrl.searchParams.delete('returnUrl')

      // 返回安全的URL
      return NextResponse.redirect(safeUrl)
    }
  }

  // 检查是否是白名单路径
  if (PUBLIC_PATHS.some(path => pathname.startsWith(path))) {
    logger.debug(`[中间件:${requestId}] 公开路径: ${pathname}, 允许访问`)

    // 创建响应并添加 CORS 头
    const response = NextResponse.next()

    // 获取域名
    const origin = request.headers.get('origin') || "https://ooaclkofmixc.sealoshzh.site"

    // 添加 CORS 头
    response.headers.set('Access-Control-Allow-Origin', origin)
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Auth-Timestamp, X-Auth-Signature, X-Auth-App-Id, X-Auth-Login-Name, X-Auth-Org-Code')
    response.headers.set('Access-Control-Allow-Credentials', 'true')

    // 添加安全相关的HTTP头部
    if (process.env.NODE_ENV === 'production') {
      // 添加内容安全策略(CSP)
      // 禁止内联脚本错误报告
      response.headers.set(
        'Content-Security-Policy',
        "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self' data:; connect-src 'self' https://services.vcrm.vip:8000; frame-src 'self'; object-src 'none'; report-uri /api/csp-report; report-to default"
      );

      // 添加X-Frame-Options头部，防止网站被嵌入到iframe中
      response.headers.set('X-Frame-Options', 'SAMEORIGIN');

      // 添加X-Content-Type-Options头部，防止MIME类型嗅探
      response.headers.set('X-Content-Type-Options', 'nosniff');

      // 添加X-XSS-Protection头部，启用XSS过滤器
      response.headers.set('X-XSS-Protection', '1; mode=block');

      // 添加Referrer-Policy头部，控制Referer头部的发送
      response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

      // 添加Feature-Policy头部，控制浏览器特性的使用
      response.headers.set(
        'Feature-Policy',
        "camera 'none'; microphone 'none'; geolocation 'none'"
      );

      // 添加Permissions-Policy头部，控制浏览器权限的使用
      response.headers.set(
        'Permissions-Policy',
        "camera=(), microphone=(), geolocation=(), interest-cohort=()"
      );

      // 添加Cache-Control头部，控制缓存行为
      response.headers.set(
        'Cache-Control',
        'no-store, no-cache, must-revalidate, proxy-revalidate'
      );

      // 添加Pragma头部，兼容HTTP/1.0
      response.headers.set('Pragma', 'no-cache');

      // 添加Expires头部，设置过期时间
      response.headers.set('Expires', '0');
    }

    return response
  }

  // 检查是否需要认证
  const requiresAuth = AUTH_PATHS.some(path => pathname.startsWith(path)) ||
                      AUTH_LAYOUTS.some(layout => pathname.startsWith(layout))
  const needsPermission = PROTECTED_PATHS.some(path => pathname.startsWith(path))

  if (!requiresAuth && !needsPermission) {
    logger.debug(`[中间件:${requestId}] 无需认证路径: ${pathname}, 允许访问`)

    // 创建响应并添加 CORS 头
    const response = NextResponse.next()

    // 获取域名
    const origin = request.headers.get('origin') || "https://ooaclkofmixc.sealoshzh.site"

    // 添加 CORS 头
    response.headers.set('Access-Control-Allow-Origin', origin)
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Auth-Timestamp, X-Auth-Signature, X-Auth-App-Id, X-Auth-Login-Name, X-Auth-Org-Code')
    response.headers.set('Access-Control-Allow-Credentials', 'true')

    // 添加安全相关的HTTP头部
    if (process.env.NODE_ENV === 'production') {
      // 添加内容安全策略(CSP)
      // 禁止内联脚本错误报告
      response.headers.set(
        'Content-Security-Policy',
        "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self' data:; connect-src 'self' https://services.vcrm.vip:8000; frame-src 'self'; object-src 'none'; report-uri /api/csp-report; report-to default"
      );

      // 添加X-Frame-Options头部，防止网站被嵌入到iframe中
      response.headers.set('X-Frame-Options', 'SAMEORIGIN');

      // 添加X-Content-Type-Options头部，防止MIME类型嗅探
      response.headers.set('X-Content-Type-Options', 'nosniff');

      // 添加X-XSS-Protection头部，启用XSS过滤器
      response.headers.set('X-XSS-Protection', '1; mode=block');

      // 添加Referrer-Policy头部，控制Referer头部的发送
      response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

      // 添加Feature-Policy头部，控制浏览器特性的使用
      response.headers.set(
        'Feature-Policy',
        "camera 'none'; microphone 'none'; geolocation 'none'"
      );

      // 添加Permissions-Policy头部，控制浏览器权限的使用
      response.headers.set(
        'Permissions-Policy',
        "camera=(), microphone=(), geolocation=(), interest-cohort=()"
      );

      // 添加Cache-Control头部，控制缓存行为
      response.headers.set(
        'Cache-Control',
        'no-store, no-cache, must-revalidate, proxy-revalidate'
      );

      // 添加Pragma头部，兼容HTTP/1.0
      response.headers.set('Pragma', 'no-cache');

      // 添加Expires头部，设置过期时间
      response.headers.set('Expires', '0');
    }

    return response
  }

  try {
    const authInfo = await getAuthInfo(request, requestId)

    if (!authInfo.isAuthenticated) {
      logger.debug(`[中间件:${requestId}] 未认证访问: ${pathname}, 认证类型: ${authInfo.authType}`)
      const isApiRequest = pathname.startsWith('/api/')

      if (isApiRequest) {
        return NextResponse.json({
          success: false,
          message: "未授权访问",
          requestId
        }, { status: 401 })
      }

      // 保存当前URL作为returnUrl（安全检查已在前面完成）
      const url = new URL("/login", request.url)
      url.searchParams.set("returnUrl", pathname)

      // 添加一次性令牌防止CSRF
      const csrfToken = uuidv4().substring(0, 16)
      url.searchParams.set("csrf", csrfToken)

      logger.debug(`[中间件:${requestId}] 重定向到登录页: ${url.toString()}`)
      return NextResponse.redirect(url)
    }

    // 为API请求添加用户信息到请求头
    const requestHeaders = new Headers(request.headers)
    requestHeaders.set('x-user-id', authInfo.userId || '')
    requestHeaders.set('x-user-role', authInfo.roleCode || '')
    requestHeaders.set('x-request-id', requestId)
    requestHeaders.set('x-auth-type', authInfo.authType || '')

    if (authInfo.permissions) {
      requestHeaders.set('x-user-permissions', JSON.stringify(authInfo.permissions))
    }

    // 如果是需要权限检查的路径，检查用户权限
    if (needsPermission) {
      const resource = pathname.split('/').pop() || ''
      const hasPermission = await checkUserPermission(
        authInfo.userId || '',
        resource,
        'access',
        requestId
      )

      if (!hasPermission) {
        logger.warn(`[中间件:${requestId}] 权限不足: 用户=${authInfo.userId}, 资源=${resource}`)
        return NextResponse.json({
          success: false,
          message: "无权访问此资源",
          requestId
        }, { status: 403 })
      }
    }

    logger.debug(`[中间件:${requestId}] 认证成功: ${pathname}, 用户=${authInfo.userId}`)

    // 继续请求，带上用户信息
    const response = NextResponse.next({
      request: {
        headers: requestHeaders
      }
    })

    // 获取域名
    const origin = request.headers.get('origin') || "https://ooaclkofmixc.sealoshzh.site"

    // 添加 CORS 头
    response.headers.set('Access-Control-Allow-Origin', origin)
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Auth-Timestamp, X-Auth-Signature, X-Auth-App-Id, X-Auth-Login-Name, X-Auth-Org-Code')
    response.headers.set('Access-Control-Allow-Credentials', 'true')

    // 添加安全相关的HTTP头部
    if (process.env.NODE_ENV === 'production') {
      // 添加内容安全策略(CSP)
      // 禁止内联脚本错误报告
      response.headers.set(
        'Content-Security-Policy',
        "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self' data:; connect-src 'self' https://services.vcrm.vip:8000; frame-src 'self'; object-src 'none'; report-uri /api/csp-report; report-to default"
      );

      // 添加X-Frame-Options头部，防止网站被嵌入到iframe中
      response.headers.set('X-Frame-Options', 'SAMEORIGIN');

      // 添加X-Content-Type-Options头部，防止MIME类型嗅探
      response.headers.set('X-Content-Type-Options', 'nosniff');

      // 添加X-XSS-Protection头部，启用XSS过滤器
      response.headers.set('X-XSS-Protection', '1; mode=block');

      // 添加Referrer-Policy头部，控制Referer头部的发送
      response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

      // 添加Feature-Policy头部，控制浏览器特性的使用
      response.headers.set(
        'Feature-Policy',
        "camera 'none'; microphone 'none'; geolocation 'none'"
      );

      // 添加Permissions-Policy头部，控制浏览器权限的使用
      response.headers.set(
        'Permissions-Policy',
        "camera=(), microphone=(), geolocation=(), interest-cohort=()"
      );

      // 添加Cache-Control头部，控制缓存行为
      response.headers.set(
        'Cache-Control',
        'no-store, no-cache, must-revalidate, proxy-revalidate'
      );

      // 添加Pragma头部，兼容HTTP/1.0
      response.headers.set('Pragma', 'no-cache');

      // 添加Expires头部，设置过期时间
      response.headers.set('Expires', '0');
    }

    return response
  } catch (error) {
    logger.error(`[中间件:${requestId}] 处理错误:`, error)
    return NextResponse.json({
      success: false,
      message: "服务器内部错误",
      requestId
    }, { status: 500 })
  }
}

export const config = {
  matcher: [
    /*
     * 匹配所有路径，除了:
     * - API路由中的NextAuth路由
     * - 静态文件路由
     * - public目录下的静态文件
     * - dashboard相关路径（让NextAuth在页面级别处理）
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico|login|register|forgot-password|dashboard|.*\\.(?:png|jpg|jpeg|gif|svg|ico|webp|avif|css|js|woff|woff2|ttf|eot)).*)',
    '/api/:path*', // 匹配所有 API 路由，确保 CORS 头被添加
  ],
}